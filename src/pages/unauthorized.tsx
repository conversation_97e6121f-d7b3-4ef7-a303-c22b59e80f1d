import type { NextPageWithLayout } from './_app';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/atoms';

const UnauthorizedPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { user, logout } = useAuthStore();

	const handleGoBack = () => {
		router.back();
	};

	const handleGoHome = () => {
		// Redirect based on user role
		if (user?.role === 'ADMIN') {
			router.push('/app/admin');
		} else {
			router.push('/app');
		}
	};

	const handleSignOut = async () => {
		await logout();
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8 text-center">
				<div>
					<div className="mx-auto h-24 w-24 bg-red-100 rounded-full flex items-center justify-center">
						<svg
							className="h-12 w-12 text-red-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z"
							/>
						</svg>
					</div>
					<h2 className="mt-6 text-3xl font-extrabold text-gray-900">
						Access Denied
					</h2>
					<p className="mt-2 text-sm text-gray-600">
						You don't have permission to access this page.
					</p>
					{user && (
						<p className="mt-1 text-xs text-gray-500">
							Signed in as: {user.email} ({user.role})
						</p>
					)}
				</div>

				<div className="space-y-4">
					<Button
						onClick={handleGoBack}
						variant="primary"
						className="w-full"
					>
						Go Back
					</Button>
					
					<Button
						onClick={handleGoHome}
						variant="secondary"
						className="w-full"
					>
						Go to Dashboard
					</Button>
					
					<Button
						onClick={handleSignOut}
						variant="outline"
						className="w-full text-red-600 border-red-300 hover:bg-red-50"
					>
						Sign Out
					</Button>
				</div>
			</div>
		</div>
	);
};

export default UnauthorizedPage;
