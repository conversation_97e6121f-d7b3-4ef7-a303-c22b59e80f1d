import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import { withAdminGuards } from '@/components/guards';
import { useAuthStore } from '@/stores/authStore';

const AdminDashboardPage: NextPageWithLayout = () => {
	const { user } = useAuthStore();

	return (
		<main className="p-6">
			<div className="mb-6">
				<h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
				<p className="text-gray-600 mt-2">
					Welcome back, {user?.fullName || user?.email}
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">User Management</h2>
					<p className="text-gray-600 mb-4">
						Manage user accounts, roles, and permissions.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						Manage Users
					</button>
				</div>

				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">System Settings</h2>
					<p className="text-gray-600 mb-4">
						Configure system-wide settings and preferences.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						Settings
					</button>
				</div>

				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">Analytics</h2>
					<p className="text-gray-600 mb-4">
						View system analytics and usage statistics.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						View Analytics
					</button>
				</div>

				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">Invitations</h2>
					<p className="text-gray-600 mb-4">
						Send invitations to new users and manage pending invites.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						Manage Invitations
					</button>
				</div>

				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">Audit Logs</h2>
					<p className="text-gray-600 mb-4">
						Review system audit logs and security events.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						View Logs
					</button>
				</div>

				<div className="bg-white rounded-lg shadow p-6">
					<h2 className="text-xl font-semibold mb-4">Backup & Recovery</h2>
					<p className="text-gray-600 mb-4">
						Manage system backups and recovery options.
					</p>
					<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
						Backup Settings
					</button>
				</div>
			</div>
		</main>
	);
};

AdminDashboardPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default withAdminGuards(AdminDashboardPage);
