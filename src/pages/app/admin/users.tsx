import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '@/pages/_app';
import { withAdminGuards } from '@/components/guards';
import { useAuthStore } from '@/stores/authStore';

const UserManagementPage: NextPageWithLayout = () => {
	const { user } = useAuthStore();

	// Mock user data - in real app this would come from API
	const users = [
		{
			id: '1',
			email: '<EMAIL>',
			fullName: '<PERSON>',
			role: 'USER',
			status: 'ACTIVE',
			createdAt: '2024-01-15',
		},
		{
			id: '2',
			email: '<EMAIL>',
			fullName: '<PERSON>',
			role: 'USER',
			status: 'INACTIVE',
			createdAt: '2024-01-20',
		},
		{
			id: '3',
			email: '<EMAIL>',
			fullName: 'Admin User',
			role: 'ADMIN',
			status: 'ACTIVE',
			createdAt: '2024-01-01',
		},
	];

	const getStatusBadge = (status: string) => {
		const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
		switch (status) {
			case 'ACTIVE':
				return `${baseClasses} bg-green-100 text-green-800`;
			case 'INACTIVE':
				return `${baseClasses} bg-yellow-100 text-yellow-800`;
			case 'REJECTED':
				return `${baseClasses} bg-red-100 text-red-800`;
			default:
				return `${baseClasses} bg-gray-100 text-gray-800`;
		}
	};

	const getRoleBadge = (role: string) => {
		const baseClasses = 'px-2 py-1 text-xs font-medium rounded-full';
		switch (role) {
			case 'ADMIN':
				return `${baseClasses} bg-purple-100 text-purple-800`;
			case 'USER':
				return `${baseClasses} bg-blue-100 text-blue-800`;
			default:
				return `${baseClasses} bg-gray-100 text-gray-800`;
		}
	};

	return (
		<main className="p-6">
			<div className="mb-6">
				<h1 className="text-3xl font-bold text-gray-900">User Management</h1>
				<p className="text-gray-600 mt-2">
					Manage user accounts, roles, and permissions
				</p>
			</div>

			<div className="bg-white rounded-lg shadow overflow-hidden">
				<div className="px-6 py-4 border-b border-gray-200">
					<div className="flex justify-between items-center">
						<h2 className="text-lg font-medium text-gray-900">All Users</h2>
						<button className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90">
							Invite User
						</button>
					</div>
				</div>

				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									User
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Role
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Created
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{users.map((userData) => (
								<tr key={userData.id}>
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{userData.fullName}
											</div>
											<div className="text-sm text-gray-500">
												{userData.email}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className={getRoleBadge(userData.role)}>
											{userData.role}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className={getStatusBadge(userData.status)}>
											{userData.status}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{userData.createdAt}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<button className="text-primary hover:text-primary/80 mr-4">
											Edit
										</button>
										{userData.id !== user?.id && (
											<button className="text-red-600 hover:text-red-500">
												Delete
											</button>
										)}
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</main>
	);
};

UserManagementPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default withAdminGuards(UserManagementPage);
