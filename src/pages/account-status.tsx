import type { NextPageWithLayout } from './_app';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/atoms';
import { withAuthOnly } from '@/components/guards';

const AccountStatusPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { user, logout } = useAuthStore();

	const handleSignOut = async () => {
		await logout();
	};

	const handleContactSupport = () => {
		// You can implement this to open a support ticket or email
		window.location.href = 'mailto:<EMAIL>?subject=Account Status Inquiry';
	};

	if (!user) {
		return null; // This should not happen due to withAuthOnly guard
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div className="text-center">
					{user.status === 'INACTIVE' && (
						<>
							<div className="mx-auto h-24 w-24 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
								<svg className="h-12 w-12 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z" />
								</svg>
							</div>
							<h1 className="text-3xl font-bold text-gray-900 mb-4">Account Inactive</h1>
							<p className="text-gray-600 mb-6">
								Your account is currently inactive. This may be because:
							</p>
							<ul className="text-left text-gray-600 mb-6 space-y-2">
								<li>• Your account is pending approval</li>
								<li>• Additional verification is required</li>
								<li>• Your account has been temporarily suspended</li>
							</ul>
							<p className="text-gray-600 mb-8">
								Please contact an administrator to activate your account or get more information about your account status.
							</p>
						</>
					)}
					
					{user.status === 'REJECTED' && (
						<>
							<div className="mx-auto h-24 w-24 bg-red-100 rounded-full flex items-center justify-center mb-6">
								<svg className="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
								</svg>
							</div>
							<h1 className="text-3xl font-bold text-gray-900 mb-4">Account Rejected</h1>
							<p className="text-gray-600 mb-6">
								Your account application has been rejected. This could be due to:
							</p>
							<ul className="text-left text-gray-600 mb-6 space-y-2">
								<li>• Incomplete or invalid information provided</li>
								<li>• Failure to meet eligibility requirements</li>
								<li>• Policy violations</li>
							</ul>
							<p className="text-gray-600 mb-8">
								If you believe this is an error or would like to appeal this decision, please contact our support team.
							</p>
						</>
					)}

					{user.status === 'ACTIVE' && (
						<>
							<div className="mx-auto h-24 w-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
								<svg className="h-12 w-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
								</svg>
							</div>
							<h1 className="text-3xl font-bold text-gray-900 mb-4">Account Active</h1>
							<p className="text-gray-600 mb-8">
								Your account is active and in good standing. You can access all features of the application.
							</p>
						</>
					)}

					<div className="bg-gray-100 rounded-lg p-4 mb-8">
						<p className="text-sm text-gray-600">
							<strong>Account Details:</strong>
						</p>
						<p className="text-sm text-gray-800">Email: {user.email}</p>
						<p className="text-sm text-gray-800">Role: {user.role}</p>
						<p className="text-sm text-gray-800">Status: {user.status}</p>
					</div>
				</div>

				<div className="space-y-4">
					{user.status === 'ACTIVE' && (
						<Button
							onClick={() => router.push('/app')}
							variant="primary"
							className="w-full"
						>
							Go to Dashboard
						</Button>
					)}
					
					{(user.status === 'INACTIVE' || user.status === 'REJECTED') && (
						<Button
							onClick={handleContactSupport}
							variant="primary"
							className="w-full"
						>
							Contact Support
						</Button>
					)}
					
					<Button
						onClick={handleSignOut}
						variant="outline"
						className="w-full"
					>
						Sign Out
					</Button>
				</div>
			</div>
		</div>
	);
};

export default withAuthOnly(AccountStatusPage);
