import { Button } from '@/components/atoms';
import { ThemeToggle } from '@/components/molecules';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/router';

export default function Home() {
	const { user, logout, isLoading } = useAuthStore();
	const router = useRouter();
	return (
		<div className='flex flex-col items-center justify-center min-h-screen p-4 bg-background text-foreground'>
			<h1 className='text-2xl font-bold'>Welcome to PixiGenerator</h1>
			<p className='mt-2 text-lg'>
				{user ? `Hello, ${user.email}` : 'Please sign in to continue.'}
			</p>
			<p className='mt-4 text-sm text-muted-foreground'>
				{user ? 'You are logged in.' : 'You are not logged in.'}
			</p>
			<p className='mt-2 text-sm text-muted-foreground'>
				{user ? `Role: ${user.role}` : 'No role assigned.'}
			</p>
			<p className='mt-2 text-sm text-muted-foreground'>
				{user ? `Status: ${user.status}` : 'No status available.'}
			</p>
			<p className='mt-2 text-sm text-muted-foreground'>
				{user ? `ID: ${user.id}` : 'No ID available.'}
			</p>
			<div className='mt-6 flex items-center space-x-4'>
				<ThemeToggle />
				{user ? (
					<Button
						variant='secondary'
						onClick={logout}
						disabled={isLoading}>
						{isLoading ? 'Logging out...' : 'Log out'}
					</Button>
				) : (
					<Button
						variant='primary'
						onClick={() => router.push('/signin')}>
						Sign In
					</Button>
				)}
			</div>
		</div>
	);
}
