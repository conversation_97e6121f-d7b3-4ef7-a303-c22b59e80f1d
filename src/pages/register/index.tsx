import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';
import { RegisterForm } from '@/components/organisms';
import { AuthTemplate } from '@/components/templates';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import InvitationApi from '@/api/InvitationApi';
import { Httpstatus } from '@/common/StandardApi';

const RegisterPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [invitation, setInvitation] = useState<
		| {
				token?: string;
				email?: string;
				status?: string;
		  }
		| undefined
	>(undefined);
	const [invitationError, setInvitationError] = useState<string | undefined>(
		undefined,
	);
	const [isLoadingInvitation, setIsLoadingInvitation] = useState(false);

	useEffect(() => {
		const invitationToken = router.query.invitation as string;

		if (router.isReady && invitationToken) {
			setIsLoadingInvitation(true);

			const fetchInvitation = async () => {
				try {
					const invitationApi = new InvitationApi();
					const response = await invitationApi.getInvitation(invitationToken);

					if (response.status === Httpstatus.SuccessOK) {
						setInvitation({
							token: invitationToken,
							email: response.data.email,
							status: response.data.status,
						});
						setInvitationError(undefined);
					} else {
						setInvitationError('Invalid or expired invitation token');
						setInvitation(undefined);
					}
				} catch (error) {
					console.error('Error fetching invitation:', error);
					setInvitationError('Failed to validate invitation token');
					setInvitation(undefined);
				} finally {
					setIsLoadingInvitation(false);
				}
			};

			fetchInvitation();
		} else if (router.isReady) {
			// No invitation token, just show normal registration
			setIsLoadingInvitation(false);
		}
	}, [router.isReady, router.query.invitation]);

	return (
		<div className='flex w-md min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<RegisterForm
				invitation={invitation}
				invitationError={invitationError}
				isLoadingInvitation={isLoadingInvitation}
			/>

			<div className='mt-6 text-sm text-muted-foreground'>
				<p>
					Do you have an account?{' '}
					<Link
						href='signin'
						className='font-semibold text-primary hover:underline'>
						Login
					</Link>
				</p>
			</div>
		</div>
	);
};

RegisterPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export default RegisterPage;
