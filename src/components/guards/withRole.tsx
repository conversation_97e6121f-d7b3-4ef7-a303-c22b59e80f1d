import { useEffect, ComponentType } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';

type UserRole = 'ADMIN' | 'USER';

interface RoleGuardOptions {
	allowedRoles: UserRole[];
	redirectTo?: string;
	fallbackComponent?: ComponentType;
}

/**
 * HOC that restricts access based on user roles
 * Assumes user is already authenticated (use with withAuth)
 */
export function withRole<P extends object>(
	options: RoleGuardOptions
): (WrappedComponent: ComponentType<P>) => ComponentType<P> {
	return function (WrappedComponent: ComponentType<P>): ComponentType<P> {
		const RoleGuardedComponent = (props: P) => {
			const router = useRouter();
			const { user, isHydrated } = useAuthStore();
			const { allowedRoles, redirectTo = '/unauthorized', fallbackComponent: FallbackComponent } = options;

			useEffect(() => {
				// Wait for store to hydrate
				if (!isHydrated || !user) return;

				// Check if user's role is allowed
				if (!allowedRoles.includes(user.role)) {
					if (FallbackComponent) {
						// Don't redirect, will show fallback component
						return;
					}
					
					// Redirect to unauthorized page or specified route
					router.replace(redirectTo);
					return;
				}
			}, [user, isHydrated, router]);

			// Show loading while checking
			if (!isHydrated || !user) {
				return (
					<div className="flex items-center justify-center min-h-screen">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
					</div>
				);
			}

			// Check role authorization
			if (!allowedRoles.includes(user.role)) {
				// Show fallback component if provided
				if (FallbackComponent) {
					return <FallbackComponent {...(props as any)} />;
				}
				
				// Show unauthorized message while redirecting
				return (
					<div className="flex items-center justify-center min-h-screen">
						<div className="text-center">
							<h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
							<p className="text-muted-foreground">You don't have permission to access this page.</p>
							<p className="text-sm text-muted-foreground mt-2">Redirecting...</p>
						</div>
					</div>
				);
			}

			// User has required role, render the component
			return <WrappedComponent {...props} />;
		};

		// Set display name for debugging
		RoleGuardedComponent.displayName = `withRole(${allowedRoles.join(',')})(${WrappedComponent.displayName || WrappedComponent.name})`;

		return RoleGuardedComponent;
	};
}

// Convenience functions for common role combinations
export const withAdminRole = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withRole<P>({ allowedRoles: ['ADMIN'] })(WrappedComponent);

export const withUserRole = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withRole<P>({ allowedRoles: ['USER'] })(WrappedComponent);

export const withAnyRole = <P extends object>(WrappedComponent: ComponentType<P>) =>
	withRole<P>({ allowedRoles: ['ADMIN', 'USER'] })(WrappedComponent);
