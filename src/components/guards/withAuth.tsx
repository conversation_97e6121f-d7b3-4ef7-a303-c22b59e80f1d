import { useEffect, ComponentType } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';

/**
 * HOC that ensures user is authenticated before accessing protected routes
 * Redirects to signin page if user is not authenticated
 */
export function withAuth<P extends object>(
	WrappedComponent: ComponentType<P>
): ComponentType<P> {
	const AuthenticatedComponent = (props: P) => {
		const router = useRouter();
		const { user, accessToken, isHydrated } = useAuthStore();

		useEffect(() => {
			// Wait for store to hydrate before making decisions
			if (!isHydrated) return;

			// Check if user is authenticated
			if (!user || !accessToken) {
				// Store the current path for redirect after login
				const currentPath = router.asPath;
				const redirectUrl = currentPath.startsWith('/app') ? currentPath : '/app';
				
				router.replace(`/signin?redirect=${encodeURIComponent(redirectUrl)}`);
				return;
			}
		}, [user, accessToken, isHydrated, router]);

		// Show loading or nothing while checking authentication
		if (!isHydrated || !user || !accessToken) {
			return (
				<div className="flex items-center justify-center min-h-screen">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
				</div>
			);
		}

		// User is authenticated, render the component
		return <WrappedComponent {...props} />;
	};

	// Set display name for debugging
	AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

	return AuthenticatedComponent;
}
