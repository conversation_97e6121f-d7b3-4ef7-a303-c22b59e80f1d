import { useEffect, ComponentType } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';

type UserStatus = 'ACTIVE' | 'INACTIVE' | 'REJECTED';

interface StatusGuardOptions {
	allowedStatuses?: UserStatus[];
	redirectTo?: string;
	fallbackComponent?: ComponentType;
}

/**
 * HOC that blocks users with INACTIVE/REJECTED status from using the app
 * while keeping them authenticated. Assumes user is already authenticated.
 */
export function withActiveStatus<P extends object>(
	options: StatusGuardOptions = {},
): (WrappedComponent: ComponentType<P>) => ComponentType<P> {
	return function (WrappedComponent: ComponentType<P>): ComponentType<P> {
		const StatusGuardedComponent = (props: P) => {
			const router = useRouter();
			const { user, isHydrated } = useAuthStore();
			const {
				allowedStatuses = ['ACTIVE'],
				redirectTo = '/account-status',
				fallbackComponent: FallbackComponent,
			} = options;

			useEffect(() => {
				// Wait for store to hydrate
				if (!isHydrated || !user) return;

				// Check if user's status is allowed
				if (!allowedStatuses.includes(user.status)) {
					if (redirectTo) {
						router.replace(redirectTo);
						return;
					}
					// If no redirect specified, component will show fallback or status message
				}
			}, [user, isHydrated, router]);

			// Show loading while checking
			if (!isHydrated || !user) {
				return (
					<div className='flex items-center justify-center min-h-screen'>
						<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
					</div>
				);
			}

			// Check status authorization
			if (!allowedStatuses.includes(user.status)) {
				// Show fallback component if provided
				if (FallbackComponent) {
					return <FallbackComponent {...(props as any)} />;
				}

				// Show status-specific message
				return (
					<div className='flex items-center justify-center min-h-screen p-6'>
						<div className='max-w-md text-center'>
							{user.status === 'INACTIVE' && (
								<>
									<div className='mb-6'>
										<div className='w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4'>
											<svg
												className='w-8 h-8 text-yellow-600'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z'
												/>
											</svg>
										</div>
									</div>
									<h1 className='text-2xl font-bold text-gray-900 mb-4'>
										Account Inactive
									</h1>
									<p className='text-gray-600 mb-6'>
										Your account is currently inactive. Please contact an
										administrator to activate your account.
									</p>
								</>
							)}

							{user.status === 'REJECTED' && (
								<>
									<div className='mb-6'>
										<div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4'>
											<svg
												className='w-8 h-8 text-red-600'
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'>
												<path
													strokeLinecap='round'
													strokeLinejoin='round'
													strokeWidth={2}
													d='M6 18L18 6M6 6l12 12'
												/>
											</svg>
										</div>
									</div>
									<h1 className='text-2xl font-bold text-gray-900 mb-4'>
										Access Denied
									</h1>
									<p className='text-gray-600 mb-6'>
										Your account has been rejected. Please contact an
										administrator for more information.
									</p>
								</>
							)}

							<div className='space-y-3'>
								<button
									onClick={() => router.push('/signin')}
									className='w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors'>
									Sign Out
								</button>
								<p className='text-sm text-gray-500'>
									Signed in as: {user.email}
								</p>
							</div>
						</div>
					</div>
				);
			}

			// User has active status, render the component
			return <WrappedComponent {...props} />;
		};

		// Set display name for debugging
		StatusGuardedComponent.displayName = `withActiveStatus(${allowedStatuses.join(
			',',
		)})(${WrappedComponent.displayName || WrappedComponent.name})`;

		return StatusGuardedComponent;
	};
}

// Convenience function for active status only (most common use case)
export const withActiveStatusOnly = <P extends object>(
	WrappedComponent: ComponentType<P>,
) => withActiveStatus<P>()(WrappedComponent);
