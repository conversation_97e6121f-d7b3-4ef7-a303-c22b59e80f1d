import { useState } from 'react';
import { useRouter } from 'next/router';
import { AuthAPI } from '@/api/AuthApi';
import { useAuthStore } from '@/stores/authStore';
import { Httpstatus } from '@/common/StandardApi';

interface LoginCredentials {
	email: string;
	password: string;
}

interface RegisterCredentials {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

export const useAuth = () => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const { setTokens, logout: authLogout } = useAuthStore();

	const clearError = () => setError(null);

	const extractTokens = (response: any) => {
		let accessToken, refreshToken;

		// According to backend API spec, tokens are returned directly in response.data
		if (response.data?.accessToken) {
			accessToken = response.data.accessToken;
			refreshToken = response.data.refreshToken;
		}

		return { accessToken, refreshToken };
	};

	const authenticateUser = async (
		accessToken: string,
		refreshToken: string,
		userData?: any,
	) => {
		if (
			accessToken &&
			refreshToken &&
			typeof accessToken === 'string' &&
			typeof refreshToken === 'string'
		) {
			await setTokens(accessToken, refreshToken);

			// If we have user data from the API response, update the user info
			if (userData) {
				const { setUser } = useAuthStore.getState();
				setUser({
					id: userData.id,
					fullName: userData.fullName || userData.email?.split('@')[0] || '',
					email: userData.email,
					role: userData.role,
					status: userData.status,
				});
			}

			// Check if there's a redirect URL from middleware
			const redirectUrl = router.query.redirect as string;
			const destination =
				redirectUrl && redirectUrl.startsWith('/app') ? redirectUrl : '/app';

			router.push(destination);
			return true;
		}
		return false;
	};

	const login = async (credentials: LoginCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login(credentials);

			if (
				response.status === Httpstatus.SuccessOK ||
				response.status === Httpstatus.SuccessCreated
			) {
				const { accessToken, refreshToken } = extractTokens(response);
				const userData = response.data; // Get user data from API response

				if (await authenticateUser(accessToken, refreshToken, userData)) {
					return { success: true };
				} else {
					setError('Login successful but tokens are invalid or missing.');
					return { success: false };
				}
			} else {
				setError(
					response.data?.message ||
						response.data?.error ||
						`Login failed (Status: ${response.status}). Please try again.`,
				);
				return { success: false };
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (credentials: RegisterCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			// Check if this is an invitation acceptance
			if (credentials.invitationToken) {
				const InvitationApi = (await import('@/api/InvitationApi')).default;
				const invitationApi = new InvitationApi();

				const response = await invitationApi.acceptInvitation({
					token: credentials.invitationToken,
					password: credentials.password,
					fullName: credentials.fullName,
				});

				if (response.status === Httpstatus.SuccessOK) {
					// Invitation accepted successfully, now auto-login
					try {
						const authApi = new AuthAPI();
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						if (
							loginResponse.status === Httpstatus.SuccessOK ||
							loginResponse.status === Httpstatus.SuccessCreated
						) {
							const { accessToken, refreshToken } = extractTokens(loginResponse);
							const userData = loginResponse.data;

							if (await authenticateUser(accessToken, refreshToken, userData)) {
								return { success: true };
							}
						}
					} catch (loginError) {
						console.error('Auto-login after invitation acceptance failed:', loginError);
						setError(
							'Invitation accepted successfully, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}
				} else {
					setError(
						response.data?.message ||
							response.data?.error ||
							`Invitation acceptance failed (Status: ${response.status}). Please try again.`,
					);
					return { success: false };
				}
			} else {
				// Regular registration
				const authApi = new AuthAPI();
				const response = await authApi.register(credentials);

				if (response.status === Httpstatus.SuccessCreated) {
					// Registration successful, now auto-login as per user preference
					try {
						const loginResponse = await authApi.login({
							email: credentials.email,
							password: credentials.password,
						});

						if (
							loginResponse.status === Httpstatus.SuccessOK ||
							loginResponse.status === Httpstatus.SuccessCreated
						) {
							const { accessToken, refreshToken } = extractTokens(loginResponse);
							const userData = loginResponse.data; // Get user data from login response

							if (await authenticateUser(accessToken, refreshToken, userData)) {
								return { success: true };
							}
						}
					} catch (loginError) {
						console.error('Auto-login failed:', loginError);
						setError(
							'Registration successful, but auto-login failed. Please sign in manually.',
						);
						return { success: false };
					}

					// Fallback: redirect to signin with success message
					router.push('/signin?message=Registration successful. Please sign in.');
					return { success: true, requiresSignin: true };
				} else {
					setError(
						response.data?.message ||
							response.data?.error ||
							`Registration failed (Status: ${response.status}). Please try again.`,
					);
					return { success: false };
				}
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async () => {
		setIsLoading(true);
		try {
			await authLogout();
		} catch (error) {
			console.error('Logout failed:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return {
		isLoading,
		error,
		clearError,
		login,
		register,
		logout,
	};
};
