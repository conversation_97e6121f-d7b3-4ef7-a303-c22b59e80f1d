import { useState } from 'react';
import { useRouter } from 'next/router';
import { AuthAPI } from '@/api/AuthApi';
import { useAuthStore } from '@/stores/authStore';
import { Httpstatus } from '@/common/StandardApi';

interface LoginCredentials {
	email: string;
	password: string;
}

interface RegisterCredentials {
	fullName: string;
	email: string;
	password: string;
	invitationToken?: string;
}

export const useAuth = () => {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const { setTokens, logout: authLogout } = useAuthStore();

	const clearError = () => setError(null);

	const extractTokens = (response: any) => {
		let accessToken, refreshToken;

		if (response.data?.accessToken) {
			accessToken = response.data.accessToken?.value;
			refreshToken = response.data.refreshToken?.value;
		}

		return { accessToken, refreshToken };
	};

	const authenticateUser = async (
		accessToken: string,
		refreshToken: string,
	) => {
		if (
			accessToken &&
			refreshToken &&
			typeof accessToken === 'string' &&
			typeof refreshToken === 'string'
		) {
			await setTokens(accessToken, refreshToken);

			// Check if there's a redirect URL from middleware
			const redirectUrl = router.query.redirect as string;
			const destination =
				redirectUrl && redirectUrl.startsWith('/app') ? redirectUrl : '/app';

			router.push(destination);
			return true;
		}
		return false;
	};

	const login = async (credentials: LoginCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.login(credentials);

			if (
				response.status === Httpstatus.SuccessOK ||
				response.status === Httpstatus.SuccessCreated
			) {
				const { accessToken, refreshToken } = extractTokens(response);

				if (await authenticateUser(accessToken, refreshToken)) {
					return { success: true };
				} else {
					setError('Login successful but tokens are invalid or missing.');
					return { success: false };
				}
			} else {
				setError(
					response.data?.message ||
						response.data?.error ||
						`Login failed (Status: ${response.status}). Please try again.`,
				);
				return { success: false };
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (credentials: RegisterCredentials) => {
		setIsLoading(true);
		setError(null);

		try {
			const authApi = new AuthAPI();
			const response = await authApi.register(credentials);

			if (response.status === Httpstatus.SuccessCreated) {
				// Try to get tokens from registration response first
				const { accessToken, refreshToken } = extractTokens(response);

				if (await authenticateUser(accessToken, refreshToken)) {
					return { success: true };
				}

				// If no tokens from registration, try auto-login
				try {
					const loginResponse = await authApi.login({
						email: credentials.email,
						password: credentials.password,
					});

					if (
						loginResponse.status === Httpstatus.SuccessOK ||
						loginResponse.status === Httpstatus.SuccessCreated
					) {
						const {
							accessToken: loginAccessToken,
							refreshToken: loginRefreshToken,
						} = extractTokens(loginResponse);

						if (await authenticateUser(loginAccessToken, loginRefreshToken)) {
							return { success: true };
						}
					}
				} catch (loginError) {
					console.error('Auto-login failed:', loginError);
				}

				// Fallback: redirect to signin with success message
				router.push('/signin?message=Registration successful. Please sign in.');
				return { success: true, requiresSignin: true };
			} else {
				setError(
					response.data?.message || 'Registration failed. Please try again.',
				);
				return { success: false };
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
			return { success: false };
		} finally {
			setIsLoading(false);
		}
	};

	const logout = async () => {
		setIsLoading(true);
		try {
			await authLogout();
		} catch (error) {
			console.error('Logout failed:', error);
		} finally {
			setIsLoading(false);
		}
	};

	return {
		isLoading,
		error,
		clearError,
		login,
		register,
		logout,
	};
};
