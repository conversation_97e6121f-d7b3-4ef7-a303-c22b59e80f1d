import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

class InvitationApi {
	_api: Api;
	appOrigin: string;

	constructor(appOrigin: string = 'BO') {
		this._api = new Api(ApiURL);
		this.appOrigin = appOrigin;
	}

	async acceptInvitation(body: any) {
		return await this._api.post(
			`invitation/accept`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: false,
			}),
		);
	}

	async getInvitation(token: string) {
		return await this._api.get(
			`invitation/check-token/${token}`,
			CommonFunction.createHeaders({
				withToken: false,
			}),
		);
	}
}
export default InvitationApi;
